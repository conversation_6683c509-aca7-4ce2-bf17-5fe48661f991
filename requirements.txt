# Core web framework and async support
fastapi
uvicorn[standard]
websockets

# Database and Snowflake connectivity
snowflake-connector-python
sqlalchemy
snowflake-sqlalchemy
pyarrow # Compatible version for snowflake-connector-python

# Authentication and security
python-jose[cryptography]
passlib[bcrypt]
python-multipart

# AI and LLM integration (only what's actually used)
openai

# Data processing and utilities
pydantic
pydantic-settings
python-dotenv
requests

# Development and testing
pytest
pytest-asyncio
black
flake8

# Frontend serving (for development)
jinja2
aiofiles
