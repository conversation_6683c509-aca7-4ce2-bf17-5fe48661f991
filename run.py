#!/usr/bin/env python3
"""
Simple startup script for the Technician Chatbot application.
This script provides an easy way to run the application without <PERSON><PERSON>.
"""

import os
import sys
import subprocess
import signal
import time
from pathlib import Path


def print_status(message):
    """Print status message in green."""
    print(f"\033[92m[INFO]\033[0m {message}")


def print_warning(message):
    """Print warning message in yellow."""
    print(f"\033[93m[WARNING]\033[0m {message}")


def print_error(message):
    """Print error message in red."""
    print(f"\033[91m[ERROR]\033[0m {message}")


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print_error("Python 3.8 or higher is required.")
        sys.exit(1)
    print_status(f"Python {sys.version.split()[0]} detected.")


def check_env_file():
    """Check if .env file exists."""
    if not Path(".env").exists():
        print_warning(".env file not found.")
        if Path(".env.example").exists():
            print_status("Creating .env from .env.example...")
            import shutil
            shutil.copy(".env.example", ".env")
            print_warning("Please edit .env file with your configuration.")
            return False
        else:
            print_error(".env.example not found. Please create .env file manually.")
            sys.exit(1)
    return True


def install_dependencies():
    """Install required dependencies."""
    print_status("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print_status("Dependencies installed successfully.")
    except subprocess.CalledProcessError as e:
        print_error(f"Failed to install dependencies: {e}")
        sys.exit(1)


def init_database():
    """Initialize database tables."""
    print_status("Initializing database...")
    try:
        from app.database import init_database
        init_database()
        print_status("Database initialized successfully.")
    except Exception as e:
        print_error(f"Failed to initialize database: {e}")
        print_warning("Please check your Snowflake configuration in .env file.")
        sys.exit(1)


def start_server(host="0.0.0.0", port=8000, reload=False):
    """Start the FastAPI server."""
    print_status(f"Starting server on http://{host}:{port}")
    
    try:
        import uvicorn
        uvicorn.run(
            "app.main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except KeyboardInterrupt:
        print_status("Server stopped by user.")
    except Exception as e:
        print_error(f"Failed to start server: {e}")
        sys.exit(1)


def check_health():
    """Check if the application is healthy."""
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print_status("✓ Application is healthy")
            return True
        else:
            print_warning("✗ Application health check failed")
            return False
    except Exception:
        print_warning("✗ Application is not responding")
        return False


def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Technician Chatbot - Simple Startup Script")
        print("")
        print("Usage: python run.py <command>")
        print("")
        print("Commands:")
        print("  install    - Install dependencies and set up the application")
        print("  start      - Start the application server")
        print("  dev        - Start in development mode (with auto-reload)")
        print("  check      - Check application health")
        print("  setup-db   - Initialize database tables")
        print("")
        print("Examples:")
        print("  python run.py install")
        print("  python run.py start")
        print("  python run.py dev")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    # Check Python version
    check_python_version()
    
    if command == "install":
        print_status("Installing Technician Chatbot...")
        
        # Check/create .env file
        env_ready = check_env_file()
        
        # Install dependencies
        install_dependencies()
        
        if env_ready:
            # Load environment variables
            from dotenv import load_dotenv
            load_dotenv()
            
            # Initialize database
            init_database()
            
            print_status("Installation complete!")
            print_status("You can now start the application with: python run.py start")
        else:
            print_warning("Please configure .env file and run 'python run.py setup-db' before starting.")
    
    elif command == "start":
        print_status("Starting Technician Chatbot...")
        
        # Check .env file
        if not check_env_file():
            print_error("Please configure .env file first.")
            sys.exit(1)
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Start server
        start_server()
    
    elif command == "dev":
        print_status("Starting Technician Chatbot in development mode...")
        
        # Check .env file
        if not check_env_file():
            print_error("Please configure .env file first.")
            sys.exit(1)
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Start server with reload
        start_server(reload=True)
    
    elif command == "check":
        print_status("Checking application health...")
        check_health()
    
    elif command == "setup-db":
        print_status("Setting up database...")
        
        # Check .env file
        if not check_env_file():
            print_error("Please configure .env file first.")
            sys.exit(1)
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Initialize database
        init_database()
    
    else:
        print_error(f"Unknown command: {command}")
        print("Run 'python run.py' for usage information.")
        sys.exit(1)


if __name__ == "__main__":
    main()
