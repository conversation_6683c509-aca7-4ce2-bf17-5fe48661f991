# Snowflake Database Configuration
SNOW<PERSON>AKE_ACCOUNT=foqukcw-aiteam-64squares
SNOWFLAKE_USER=<EMAIL>
SNOWFLAKE_PASSWORD=
SNOW<PERSON>AKE_AUTHENTICATOR=externalbrowser
SNOW<PERSON>AKE_DATABASE=TEST_DB
SNOWFLAKE_SCHEMA=PUBLIC
SNOWFLAKE_WAREHOUSE=S_WHH
SNOWFLAKE_ROLE=ACCOUNTADMIN

# JWT Authentication
JWT_SECRET_KEY=your_super_secret_jwt_key_here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# OpenAI API for AI features (optional)
OPENAI_API_KEY=your_openai_api_key_here

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=True

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000
