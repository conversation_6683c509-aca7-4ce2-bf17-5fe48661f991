"""Ticket management service."""

import logging
from datetime import datetime, timedelta
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from app.database import Ticket, TechnicianDummyData
from app.models import TicketCreate, TicketUpdate, TicketResponse

logger = logging.getLogger(__name__)


class TicketService:
    """Service for ticket management operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_recent_assigned_tickets(
        self,
        technician_email: str,
        limit: int = 10,
        days_back: int = 30
    ) -> List[Ticket]:
        """Get recent tickets assigned to a technician by email."""
        tickets = self.db.query(Ticket).filter(
            Ticket.technicianemail == technician_email
        ).limit(limit).all()

        logger.info(f"Retrieved {len(tickets)} tickets for technician {technician_email}")
        return tickets
    
    def get_ticket_by_id(self, ticket_id: str) -> Optional[Ticket]:
        """Get detailed ticket information by ticket number (ID)."""
        ticket = self.db.query(Ticket).filter(Ticket.ticketnumber == ticket_id).first()
        if ticket:
            logger.info(f"Retrieved ticket {ticket_id}")
        else:
            logger.warning(f"Ticket {ticket_id} not found")
        return ticket

    def get_ticket_by_number(self, ticket_number: str) -> Optional[Ticket]:
        """Get ticket by ticket number."""
        ticket = self.db.query(Ticket).filter(Ticket.ticketnumber == ticket_number).first()
        if ticket:
            logger.info(f"Retrieved ticket {ticket_number}")
        else:
            logger.warning(f"Ticket {ticket_number} not found")
        return ticket
    
    def update_ticket(self, ticket_id: int, ticket_update: TicketUpdate) -> Optional[Ticket]:
        """Update ticket information."""
        ticket = self.db.query(Ticket).filter(Ticket.id == ticket_id).first()
        if not ticket:
            logger.warning(f"Ticket {ticket_id} not found for update")
            return None
        
        update_data = ticket_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(ticket, field, value)
        
        ticket.updated_at = datetime.utcnow()
        
        # Set resolved_at if status is resolved or closed
        if ticket_update.status in ["resolved", "closed"] and not ticket.resolved_at:
            ticket.resolved_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(ticket)
        
        logger.info(f"Updated ticket {ticket_id}")
        return ticket
    
    def search_tickets(
        self,
        query: str,
        technician_email: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 20
    ) -> List[Ticket]:
        """Search tickets by query string."""
        search_filter = or_(
            Ticket.title.ilike(f"%{query}%"),
            Ticket.description.ilike(f"%{query}%"),
            Ticket.ticketnumber.ilike(f"%{query}%"),
            Ticket.issuetype.ilike(f"%{query}%")
        )

        query_obj = self.db.query(Ticket).filter(search_filter)

        if technician_email:
            query_obj = query_obj.filter(Ticket.technicianemail == technician_email)

        tickets = query_obj.limit(limit).all()

        logger.info(f"Found {len(tickets)} tickets matching query: {query}")
        return tickets

    def find_similar_tickets_by_issue(self, issue_description: str, limit: int = 10) -> List[Ticket]:
        """Find similar tickets based on issue description by searching TICKETS table."""
        try:
            # Search for tickets with similar issues in title, description, issuetype, subissuetype
            similar_tickets = self.db.query(Ticket).filter(
                or_(
                    Ticket.title.ilike(f"%{issue_description}%"),
                    Ticket.description.ilike(f"%{issue_description}%"),
                    Ticket.issuetype.ilike(f"%{issue_description}%"),
                    Ticket.subissuetype.ilike(f"%{issue_description}%")
                )
            ).limit(limit).all()

            logger.info(f"Found {len(similar_tickets)} similar tickets for issue: {issue_description}")
            return similar_tickets
        except Exception as e:
            logger.error(f"Failed to find similar tickets: {e}")
            return []

    def get_tickets_by_status(
        self,
        status: str,
        technician_email: Optional[str] = None,
        limit: int = 50
    ) -> List[Ticket]:
        """Get tickets by status (resolved/open)."""
        if status == "resolved":
            query_obj = self.db.query(Ticket).filter(Ticket.resolution.isnot(None))
        else:
            query_obj = self.db.query(Ticket).filter(
                or_(Ticket.resolution.is_(None), Ticket.resolution == "")
            )

        if technician_email:
            query_obj = query_obj.filter(Ticket.technicianemail == technician_email)

        tickets = query_obj.limit(limit).all()

        logger.info(f"Retrieved {len(tickets)} tickets with status {status}")
        return tickets
    
    def assign_ticket(self, ticket_id: int, technician_id: int) -> Optional[Ticket]:
        """Assign ticket to a technician."""
        ticket = self.db.query(Ticket).filter(Ticket.id == ticket_id).first()
        if not ticket:
            logger.warning(f"Ticket {ticket_id} not found for assignment")
            return None
        
        ticket.assigned_technician_id = technician_id
        ticket.updated_at = datetime.utcnow()
        
        # Update status to in_progress if it's currently open
        if ticket.status == "open":
            ticket.status = "in_progress"
        
        self.db.commit()
        self.db.refresh(ticket)
        
        logger.info(f"Assigned ticket {ticket_id} to technician {technician_id}")
        return ticket
