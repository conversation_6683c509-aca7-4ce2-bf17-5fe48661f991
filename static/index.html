<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technician Support Chatbot</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .chat-container {
            height: 70vh;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        .message {
            margin-bottom: 1rem;
        }
        .message.user {
            text-align: right;
        }
        .message.bot {
            text-align: left;
        }
        .message-bubble {
            display: inline-block;
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            max-width: 70%;
            word-wrap: break-word;
        }
        .message.user .message-bubble {
            background-color: #007bff;
            color: white;
        }
        .message.bot .message-bubble {
            background-color: #e9ecef;
            color: #333;
        }
        .ticket-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin: 0.5rem 0;
            background-color: white;
        }
        .login-container {
            max-width: 400px;
            margin: 2rem auto;
        }
        .status-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Login Form -->
        <div id="loginContainer" class="login-container">
            <div class="card">
                <div class="card-header text-center">
                    <h4><i class="fas fa-user-shield"></i> Technician Login</h4>
                </div>
                <div class="card-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                    </form>
                    <div id="loginError" class="alert alert-danger mt-3" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Main Chat Interface -->
        <div id="chatContainer" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2><i class="fas fa-robot"></i> Technician Support Chatbot</h2>
                        <div>
                            <span id="technicianName" class="badge bg-primary me-2"></span>
                            <button id="logoutBtn" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <!-- Chat Messages -->
                    <div id="chatMessages" class="chat-container mb-3">
                        <div class="message bot">
                            <div class="message-bubble">
                                <i class="fas fa-robot"></i> Hello! I'm your technical support assistant. 
                                I can help you with:
                                <ul class="mt-2 mb-0">
                                    <li>View your assigned tickets</li>
                                    <li>Get ticket information</li>
                                    <li>Find similar tickets</li>
                                    <li>Get step-by-step resolutions</li>
                                    <li>Get troubleshooting help</li>
                                </ul>
                                What can I help you with today?
                            </div>
                        </div>
                    </div>

                    <!-- Message Input -->
                    <div class="input-group">
                        <input type="text" id="messageInput" class="form-control" 
                               placeholder="Type your message here..." disabled>
                        <button id="sendBtn" class="btn btn-primary" type="button" disabled>
                            <i class="fas fa-paper-plane"></i> Send
                        </button>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Quick Actions -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6><i class="fas fa-bolt"></i> Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-primary btn-sm w-100 mb-2 quick-action"
                                    data-message="Show my recent tickets">
                                <i class="fas fa-ticket-alt"></i> My Tickets
                            </button>
                            <button class="btn btn-outline-success btn-sm w-100 mb-2 quick-action"
                                    data-message="I need help with resolution">
                                <i class="fas fa-tools"></i> Get AI Resolution
                            </button>
                            <button class="btn btn-outline-warning btn-sm w-100 mb-2 quick-action"
                                    data-message="Search for similar tickets">
                                <i class="fas fa-search"></i> Find Similar Tickets
                            </button>
                            <button class="btn btn-outline-info btn-sm w-100 quick-action"
                                    data-message="Help me with general question">
                                <i class="fas fa-question-circle"></i> Ask Question
                            </button>
                        </div>
                    </div>

                    <!-- Related Information -->
                    <div id="relatedInfo" class="card" style="display: none;">
                        <div class="card-header">
                            <h6><i class="fas fa-info-circle"></i> Related Information</h6>
                        </div>
                        <div class="card-body" id="relatedContent">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let authToken = null;
        let technicianId = null;
        let websocket = null;
        let sessionId = null;

        // Generate session ID
        function generateSessionId() {
            return 'session_' + Math.random().toString(36).substr(2, 9);
        }

        // Login functionality
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password }),
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;

                    // Decode JWT to get technician info
                    try {
                        const payload = JSON.parse(atob(authToken.split('.')[1]));
                        document.getElementById('technicianName').textContent = payload.sub;
                        technicianId = payload.technician_id || payload.sub;
                    } catch (e) {
                        document.getElementById('technicianName').textContent = username;
                        technicianId = username;
                    }

                    // Show chat interface
                    document.getElementById('loginContainer').style.display = 'none';
                    document.getElementById('chatContainer').style.display = 'block';

                    // Enable chat
                    document.getElementById('messageInput').disabled = false;
                    document.getElementById('sendBtn').disabled = false;

                    // Initialize session
                    sessionId = generateSessionId();

                    // Add welcome message
                    addMessage("Welcome! I'm your technical support assistant. You can ask me about your tickets, search for help, or get troubleshooting assistance.", 'bot');

                } else {
                    const error = await response.json();
                    showLoginError(error.detail || 'Login failed');
                }
            } catch (error) {
                showLoginError('Network error. Please try again.');
            }
        });

        function showLoginError(message) {
            const errorDiv = document.getElementById('loginError');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', () => {
            authToken = null;
            technicianId = null;
            sessionId = null;

            if (websocket) {
                websocket.close();
                websocket = null;
            }

            document.getElementById('loginContainer').style.display = 'block';
            document.getElementById('chatContainer').style.display = 'none';
            document.getElementById('messageInput').disabled = true;
            document.getElementById('sendBtn').disabled = true;

            // Reset chat messages to initial state
            document.getElementById('chatMessages').innerHTML = `
                <div class="message bot">
                    <div class="message-bubble">
                        <i class="fas fa-robot"></i> Hello! I'm your technical support assistant.
                        I can help you with:
                        <ul class="mt-2 mb-0">
                            <li>View your assigned tickets</li>
                            <li>Get ticket information</li>
                            <li>Find similar tickets</li>
                            <li>Get step-by-step resolutions</li>
                            <li>Get troubleshooting help</li>
                        </ul>
                        What can I help you with today?
                    </div>
                </div>
            `;

            document.getElementById('relatedInfo').style.display = 'none';
            document.getElementById('loginError').style.display = 'none';
        });

        // Send message functionality
        async function sendMessage(message) {
            if (!message.trim() || !authToken) return;
            
            // Add user message to chat
            addMessage(message, 'user');
            
            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`,
                    },
                    body: JSON.stringify({ 
                        message: message,
                        session_id: sessionId 
                    }),
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // Add bot response
                    addMessage(data.response, 'bot');
                    
                    // Show related information if available
                    showRelatedInfo(data);
                    
                } else {
                    addMessage('Sorry, I encountered an error. Please try again.', 'bot');
                }
            } catch (error) {
                addMessage('Network error. Please check your connection.', 'bot');
            }
        }

        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';
            bubbleDiv.innerHTML = text.replace(/\n/g, '<br>');
            
            messageDiv.appendChild(bubbleDiv);
            messagesContainer.appendChild(messageDiv);
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showRelatedInfo(data) {
            const relatedInfo = document.getElementById('relatedInfo');
            const relatedContent = document.getElementById('relatedContent');
            
            let content = '';
            
            if (data.related_tickets && data.related_tickets.length > 0) {
                content += '<h6>Related Tickets:</h6>';
                data.related_tickets.forEach(ticket => {
                    content += `
                        <div class="ticket-card">
                            <strong>${ticket.ticket_number}</strong>
                            <span class="badge bg-secondary status-badge ms-2">${ticket.status}</span>
                            <br>
                            <small>${ticket.title}</small>
                        </div>
                    `;
                });
            }
            
            if (data.resolution_articles && data.resolution_articles.length > 0) {
                content += '<h6 class="mt-3">Resolution Steps:</h6>';
                data.resolution_articles.forEach(article => {
                    content += `
                        <div class="ticket-card">
                            <strong>${article.title}</strong>
                            <br>
                            <small>${article.content.substring(0, 100)}...</small>
                        </div>
                    `;
                });
            }
            
            if (content) {
                relatedContent.innerHTML = content;
                relatedInfo.style.display = 'block';
            } else {
                relatedInfo.style.display = 'none';
            }
        }

        // Event listeners
        document.getElementById('sendBtn').addEventListener('click', () => {
            const input = document.getElementById('messageInput');
            sendMessage(input.value);
            input.value = '';
        });

        document.getElementById('messageInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const input = document.getElementById('messageInput');
                sendMessage(input.value);
                input.value = '';
            }
        });

        // Quick action buttons with smart prompting
        document.querySelectorAll('.quick-action').forEach(button => {
            button.addEventListener('click', () => {
                const message = button.getAttribute('data-message');
                const buttonText = button.textContent.trim();

                // Handle different types of quick actions
                if (message.includes('resolution') || message.includes('help with')) {
                    // For resolution requests, prompt for issue description
                    promptForIssueDescription(buttonText);
                } else if (message.includes('similar tickets')) {
                    // For similar tickets, prompt for search terms
                    promptForSimilarTickets();
                } else {
                    // For direct actions like "My Tickets", send immediately
                    sendMessage(message);
                }
            });
        });

        // Function to prompt for issue description
        function promptForIssueDescription(actionType) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-question-circle"></i> Describe Your Issue
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>Please describe the technical issue you need help with:</p>
                            <textarea id="issueDescription" class="form-control" rows="4"
                                placeholder="Example: My keyboard keys are not working properly, some keys don't respond when pressed..."></textarea>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="submitIssueDescription()">
                                <i class="fas fa-paper-plane"></i> Get Help
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            // Clean up modal after hiding
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // Function to submit issue description
        function submitIssueDescription() {
            const description = document.getElementById('issueDescription').value.trim();
            if (description) {
                const message = `Help me resolve this issue: ${description}`;
                sendMessage(message);

                // Close modal
                const modal = document.querySelector('.modal.show');
                if (modal) {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    modalInstance.hide();
                }
            } else {
                alert('Please describe your issue before submitting.');
            }
        }

        // Function to prompt for similar tickets search
        function promptForSimilarTickets() {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-search"></i> Search Similar Tickets
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>Enter keywords to search for similar tickets:</p>
                            <input type="text" id="searchKeywords" class="form-control"
                                placeholder="Example: email, network, printer, password...">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="submitSimilarTicketsSearch()">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            // Clean up modal after hiding
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // Function to submit similar tickets search
        function submitSimilarTicketsSearch() {
            const keywords = document.getElementById('searchKeywords').value.trim();
            if (keywords) {
                const message = `Find similar tickets for: ${keywords}`;
                sendMessage(message);

                // Close modal
                const modal = document.querySelector('.modal.show');
                if (modal) {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    modalInstance.hide();
                }
            } else {
                alert('Please enter search keywords.');
            }
        }
    </script>
</body>
</html>
