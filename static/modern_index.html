<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Technician Assistant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;

            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f1f5f9;
            --bg-accent: #e2e8f0;

            --card-bg: #ffffff;
            --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --card-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

            --border-color: #e2e8f0;
            --border-light: #f1f5f9;

            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --text-light: #94a3b8;

            --user-msg-bg: var(--primary-color);
            --bot-msg-bg: #f8fafc;
            --input-bg: #ffffff;
            --hover-bg: #f1f5f9;

            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #64748b 0%, #475569 100%);
            --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: var(--bg-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            color: var(--text-primary);
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* Professional background pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                linear-gradient(90deg, transparent 79px, var(--border-light) 79px, var(--border-light) 81px, transparent 81px),
                linear-gradient(transparent 79px, var(--border-light) 79px, var(--border-light) 81px, transparent 81px);
            background-size: 80px 80px;
            opacity: 0.3;
            z-index: -1;
        }

        /* Subtle gradient overlay */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.02) 0%, rgba(29, 78, 216, 0.02) 100%);
            z-index: -1;
        }

        .main-container {
            display: flex;
            height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Sidebar */
        .sidebar {
            width: 320px;
            background: var(--card-bg);
            border-right: 1px solid var(--border-color);
            padding: 24px;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            box-shadow: var(--card-shadow);
        }

        .sidebar-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .logo {
            background: var(--gradient-primary);
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            box-shadow: var(--card-shadow-lg);
        }

        .logo i {
            font-size: 24px;
            color: white;
        }

        .app-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
            color: var(--primary-color);
        }

        .app-subtitle {
            font-size: 14px;
            color: var(--text-muted);
        }

        .user-profile {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 24px;
            text-align: center;
            box-shadow: var(--card-shadow);
        }

        .user-avatar {
            width: 48px;
            height: 48px;
            background: var(--gradient-success);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 20px;
            color: white;
            box-shadow: var(--card-shadow);
        }

        .user-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .user-role {
            font-size: 12px;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .quick-actions {
            flex: 1;
        }

        .quick-actions h6 {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 16px;
        }

        .quick-action {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            color: var(--text-primary);
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: var(--card-shadow);
        }

        .quick-action:hover {
            background: var(--hover-bg);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            color: var(--text-primary);
            box-shadow: var(--card-shadow-lg);
        }

        .quick-action i {
            width: 20px;
            margin-right: 12px;
            color: var(--primary-color);
        }

        .quick-action span {
            font-size: 14px;
            font-weight: 500;
        }

        .logout-btn {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 12px;
            padding: 12px;
            color: var(--danger-color);
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: auto;
            box-shadow: var(--card-shadow);
        }

        .logout-btn:hover {
            background: rgba(220, 38, 38, 0.2);
            transform: translateY(-2px);
            box-shadow: var(--card-shadow-lg);
        }

        /* Chat Area */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--card-bg);
            box-shadow: var(--card-shadow);
        }

        .chat-header {
            padding: 24px 32px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
        }

        .chat-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #00f2fe;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 32px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .message {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            animation: messageSlideIn 0.4s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: var(--user-msg-bg);
            color: white;
        }

        .message.bot .message-avatar {
            background: var(--gradient-success);
            color: white;
        }

        .message-content {
            max-width: 70%;
            padding: 16px 20px;
            border-radius: 18px;
            font-size: 15px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: var(--user-msg-bg);
            color: white;
            border-bottom-right-radius: 6px;
            box-shadow: var(--card-shadow);
        }

        .message.bot .message-content {
            background: var(--bot-msg-bg);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-bottom-left-radius: 6px;
            box-shadow: var(--card-shadow);
        }

        .typing-indicator {
            display: none;
        }

        .typing-indicator .message-content {
            background: var(--bot-msg-bg);
            border: 1px solid var(--border-color);
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
            animation: typingBounce 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        .typing-dots span:nth-child(3) { animation-delay: 0s; }

        @keyframes typingBounce {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }
        /* Chat Input */
        .chat-input {
            padding: 24px 32px;
            border-top: 1px solid var(--border-color);
        }

        .input-container {
            background: var(--input-bg);
            border: 1px solid var(--border-color);
            border-radius: 24px;
            padding: 4px;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
            box-shadow: var(--card-shadow);
        }

        .input-container:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .message-input {
            flex: 1;
            background: transparent;
            border: none;
            padding: 16px 20px;
            color: var(--text-primary);
            font-size: 15px;
            outline: none;
            resize: none;
            max-height: 120px;
            min-height: 24px;
        }

        .message-input::placeholder {
            color: var(--text-muted);
        }

        .send-button {
            background: var(--gradient-primary);
            border: none;
            width: 44px;
            height: 44px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 4px;
            box-shadow: var(--card-shadow);
        }

        .send-button:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: var(--card-shadow-lg);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Login Screen */
        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 24px;
            padding: 48px;
            width: 100%;
            max-width: 420px;
            text-align: center;
            box-shadow: var(--card-shadow-lg);
        }

        .login-logo {
            background: var(--gradient-primary);
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            box-shadow: var(--card-shadow-lg);
        }

        .login-logo i {
            font-size: 32px;
            color: white;
        }

        .login-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--primary-color);
        }

        .login-subtitle {
            color: var(--text-secondary);
            margin-bottom: 32px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .form-control {
            width: 100%;
            background: var(--input-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 16px;
            color: var(--text-primary);
            font-size: 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        .login-button {
            background: var(--gradient-primary);
            border: none;
            border-radius: 12px;
            padding: 16px;
            width: 100%;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 8px;
            box-shadow: var(--card-shadow);
        }

        .login-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--card-shadow-lg);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Scrollbar */
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.3);
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.5);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                border-right: none;
                border-bottom: 1px solid var(--border-color);
                padding: 16px;
            }

            .sidebar-header {
                margin-bottom: 16px;
            }

            .quick-actions {
                display: none;
            }

            .chat-messages {
                padding: 16px;
            }

            .chat-input {
                padding: 16px;
            }

            .message-content {
                max-width: 85%;
            }
        }

        /* Modal Styles */
        .modal-content {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            color: var(--text-primary);
            box-shadow: var(--card-shadow-lg);
        }

        .modal-header {
            border-bottom: 1px solid var(--border-color);
        }

        .modal-footer {
            border-top: 1px solid var(--border-color);
        }

        .btn-close {
            filter: invert(1);
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginContainer" class="login-container">
        <div class="login-card">
            <div class="login-logo">
                <i class="fas fa-robot"></i>
            </div>
            <h1 class="login-title">AI Technician Assistant</h1>
            <p class="login-subtitle">Sign in to access your intelligent support dashboard</p>

            <form id="loginForm">
                <div class="form-group">
                    <label class="form-label" for="username">Email Address</label>
                    <input type="email" class="form-control" id="username" placeholder="Enter your email" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="password">Password</label>
                    <input type="password" class="form-control" id="password" placeholder="Enter your password" required>
                </div>
                <button type="submit" class="login-button" id="loginBtn">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Sign In
                </button>
            </form>

            <div id="loginError" class="alert alert-danger mt-3" style="display: none;"></div>
        </div>
    </div>

    <!-- Main Chat Interface -->
    <div id="chatContainer" class="main-container" style="display: none;">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                </div>
                <h1 class="app-title">AI Assistant</h1>
                <p class="app-subtitle">Intelligent Technical Support</p>
            </div>

            <div class="user-profile">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-name" id="userName">Technician</div>
                <div class="user-role" id="userRole">Technical Support</div>
            </div>

            <div class="quick-actions">
                <h6>Quick Actions</h6>
                <div class="quick-action" data-message="Show my recent tickets">
                    <i class="fas fa-ticket-alt"></i>
                    <span>My Tickets</span>
                </div>
                <div class="quick-action" data-message="I need help with resolution">
                    <i class="fas fa-tools"></i>
                    <span>Get AI Resolution</span>
                </div>
                <div class="quick-action" data-message="Search for similar tickets">
                    <i class="fas fa-search"></i>
                    <span>Find Similar Tickets</span>
                </div>
                <div class="quick-action" data-message="Help me with general question">
                    <i class="fas fa-question-circle"></i>
                    <span>Ask Question</span>
                </div>
            </div>

            <div class="logout-btn" id="logoutBtn">
                <i class="fas fa-sign-out-alt me-2"></i>
                Sign Out
            </div>
        </div>

        <!-- Chat Area -->
        <div class="chat-area">
            <div class="chat-header">
                <div class="chat-title">AI Technical Support</div>
                <div class="chat-status">
                    <div class="status-dot"></div>
                    <span>Online</span>
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <!-- Welcome message -->
                <div class="message bot">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <strong>Welcome to AI Technical Support! 🤖</strong><br><br>
                        I'm your intelligent assistant powered by Snowflake Cortex. I can help you with:
                        <br><br>
                        • 🎫 View and manage your assigned tickets<br>
                        • 🔍 Search for similar resolved issues<br>
                        • 🛠️ Generate step-by-step troubleshooting guides<br>
                        • 💡 Get AI-powered resolution suggestions<br>
                        • 📊 Access technical documentation and best practices
                        <br><br>
                        <em>What technical challenge can I help you solve today?</em>
                    </div>
                </div>

                <!-- Typing indicator -->
                <div class="message bot typing-indicator" id="typingIndicator">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <span>AI is thinking</span>
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chat-input">
                <div class="input-container">
                    <textarea class="message-input" id="messageInput"
                             placeholder="Describe your technical issue or ask a question..."
                             rows="1" disabled></textarea>
                    <button class="send-button" id="sendBtn" disabled>
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let authToken = null;
        let technicianId = null;
        let sessionId = null;

        // Generate session ID
        function generateSessionId() {
            return 'session_' + Math.random().toString(36).substr(2, 9);
        }

        // Auto-resize textarea
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            const messageInput = document.getElementById('messageInput');

            // Auto-resize textarea
            messageInput.addEventListener('input', function() {
                autoResize(this);
            });

            // Send message on Enter (but allow Shift+Enter for new lines)
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        });

        // Login functionality
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const loginBtn = document.getElementById('loginBtn');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // Show loading state
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';

            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password }),
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;

                    // Decode JWT to get technician info
                    try {
                        const payload = JSON.parse(atob(authToken.split('.')[1]));
                        document.getElementById('userName').textContent = payload.sub || username;
                        technicianId = payload.technician_id || payload.sub;
                    } catch (e) {
                        document.getElementById('userName').textContent = username;
                        technicianId = username;
                    }

                    // Show chat interface with animation
                    document.getElementById('loginContainer').style.display = 'none';
                    document.getElementById('chatContainer').style.display = 'flex';

                    // Enable chat
                    document.getElementById('messageInput').disabled = false;
                    document.getElementById('sendBtn').disabled = false;

                    // Initialize session
                    sessionId = generateSessionId();

                    // Focus on input
                    setTimeout(() => {
                        document.getElementById('messageInput').focus();
                    }, 500);

                } else {
                    const error = await response.json();
                    let errorMessage = error.detail || 'Login failed';

                    // Provide more helpful error messages for specific cases
                    if (response.status === 503) {
                        errorMessage = 'Database service is temporarily unavailable. Please try again in a few minutes.';
                    } else if (errorMessage.includes('temporarily locked') || errorMessage.includes('Database service')) {
                        errorMessage = 'The database service is currently experiencing issues. Please contact your administrator or try again later.';
                    }

                    showLoginError(errorMessage);
                }
            } catch (error) {
                showLoginError('Network error. Please check your connection and try again.');
            } finally {
                // Reset button
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Sign In';
            }
        });

        function showLoginError(message) {
            const errorDiv = document.getElementById('loginError');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', () => {
            authToken = null;
            technicianId = null;
            sessionId = null;

            // Show login screen
            document.getElementById('chatContainer').style.display = 'none';
            document.getElementById('loginContainer').style.display = 'flex';

            // Reset form
            document.getElementById('loginForm').reset();
            document.getElementById('loginError').style.display = 'none';

            // Clear chat
            const chatMessages = document.getElementById('chatMessages');
            const welcomeMessage = chatMessages.querySelector('.message.bot:first-child');
            chatMessages.innerHTML = '';
            chatMessages.appendChild(welcomeMessage);

            // Disable input
            document.getElementById('messageInput').disabled = true;
            document.getElementById('sendBtn').disabled = true;
        });

        // Add message to chat
        function addMessage(content, sender, isTyping = false) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            if (isTyping) {
                messageDiv.classList.add('typing-indicator');
            }

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            if (isTyping) {
                messageContent.innerHTML = `
                    <span>AI is thinking</span>
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                `;
            } else {
                messageContent.innerHTML = content;
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);

            // Remove existing typing indicator
            const existingTyping = chatMessages.querySelector('.typing-indicator');
            if (existingTyping && !isTyping) {
                existingTyping.remove();
            }

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return messageDiv;
        }

        // Send message
        async function sendMessage(message = null) {
            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');

            const messageText = message || messageInput.value.trim();
            if (!messageText || !authToken) return;

            // Clear input and disable
            if (!message) {
                messageInput.value = '';
                autoResize(messageInput);
            }
            sendBtn.disabled = true;

            // Add user message
            addMessage(messageText, 'user');

            // Show typing indicator
            const typingIndicator = addMessage('', 'bot', true);

            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        message: messageText,
                        session_id: sessionId
                    }),
                });

                if (response.ok) {
                    const data = await response.json();

                    // Remove typing indicator
                    typingIndicator.remove();

                    // Add bot response with formatting
                    addMessage(formatMessage(data.response), 'bot');
                } else {
                    typingIndicator.remove();
                    addMessage('Sorry, I encountered an error. Please try again.', 'bot');
                }
            } catch (error) {
                typingIndicator.remove();
                addMessage('Network error. Please check your connection and try again.', 'bot');
            } finally {
                sendBtn.disabled = false;
                messageInput.focus();
            }
        }

        // Format message content
        function formatMessage(content) {
            // Convert markdown-like formatting to HTML
            return content
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/`(.*?)`/g, '<code>$1</code>')
                .replace(/\n/g, '<br>')
                .replace(/(\d+\.\s)/g, '<br><strong>$1</strong>')
                .replace(/(Step \d+:)/g, '<br><strong>$1</strong>')
                .replace(/(Resolution:|Steps:|Troubleshooting:|Solution:)/g, '<br><strong>$1</strong>');
        }

        // Send button click
        document.getElementById('sendBtn').addEventListener('click', sendMessage);

        // Quick action buttons with smart prompting
        document.querySelectorAll('.quick-action').forEach(button => {
            button.addEventListener('click', () => {
                const message = button.getAttribute('data-message');
                const buttonText = button.textContent.trim();

                // Handle different types of quick actions
                if (message.includes('resolution') || message.includes('help with')) {
                    // For resolution requests, prompt for issue description
                    promptForIssueDescription(buttonText);
                } else if (message.includes('similar tickets')) {
                    // For similar tickets, prompt for search terms
                    promptForSimilarTickets();
                } else {
                    // For direct actions like "My Tickets", send immediately
                    sendMessage(message);
                }
            });
        });

        // Function to prompt for issue description
        function promptForIssueDescription(actionType) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-question-circle"></i> Describe Your Issue
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>Please describe the technical issue you need help with:</p>
                            <textarea id="issueDescription" class="form-control" rows="4"
                                placeholder="Example: My keyboard keys are not working properly, some keys don't respond when pressed..."></textarea>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="submitIssueDescription()">
                                <i class="fas fa-paper-plane"></i> Get AI Help
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            // Focus on textarea
            modal.addEventListener('shown.bs.modal', () => {
                document.getElementById('issueDescription').focus();
            });

            // Clean up modal after hiding
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // Function to submit issue description
        function submitIssueDescription() {
            const description = document.getElementById('issueDescription').value.trim();
            if (description) {
                const message = `Help me resolve this issue: ${description}`;
                sendMessage(message);

                // Close modal
                const modal = document.querySelector('.modal.show');
                if (modal) {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    modalInstance.hide();
                }
            } else {
                alert('Please describe your issue before submitting.');
            }
        }

        // Function to prompt for similar tickets search
        function promptForSimilarTickets() {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-search"></i> Search Similar Tickets
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>Enter keywords to search for similar tickets:</p>
                            <input type="text" id="searchKeywords" class="form-control"
                                placeholder="Example: email, network, printer, password...">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="submitSimilarTicketsSearch()">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            // Focus on input
            modal.addEventListener('shown.bs.modal', () => {
                document.getElementById('searchKeywords').focus();
            });

            // Clean up modal after hiding
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // Function to submit similar tickets search
        function submitSimilarTicketsSearch() {
            const keywords = document.getElementById('searchKeywords').value.trim();
            if (keywords) {
                const message = `Find similar tickets for: ${keywords}`;
                sendMessage(message);

                // Close modal
                const modal = document.querySelector('.modal.show');
                if (modal) {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    modalInstance.hide();
                }
            } else {
                alert('Please enter search keywords.');
            }
        }
    </script>
</body>
</html>
